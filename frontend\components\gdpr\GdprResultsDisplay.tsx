'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { GdprScanResult, GdprCheckResult } from '@/types/gdpr';
import { GdprApiService } from '@/services/gdpr-api';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  FileText,
  Cookie,
  Eye,
  Globe,
} from 'lucide-react';

interface GdprResultsDisplayProps {
  scanResult: GdprScanResult;
}

export function GdprResultsDisplay({ scanResult }: GdprResultsDisplayProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getRiskLevelIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-4 w-4" />;
      case 'medium':
        return <Eye className="h-4 w-4" />;
      case 'low':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getCheckIcon = (check: GdprCheckResult) => {
    if (check.manualReviewRequired) {
      return <Clock className="h-4 w-4 text-yellow-500" />;
    }
    return check.passed ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const handleExport = async (format: 'pdf' | 'json' | 'csv') => {
    setIsExporting(true);
    setExportError(null);

    try {
      console.log(`📄 Exporting GDPR scan report as ${format.toUpperCase()}`);

      // REAL EXPORT - NO MOCK DATA
      const blob = await GdprApiService.exportScanReport(scanResult.scanId, format);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `gdpr-scan-${scanResult.scanId}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('✅ Export completed successfully');
    } catch (error) {
      console.error('❌ Export failed:', error);
      setExportError(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const groupChecksByCategory = (checks: GdprCheckResult[]) => {
    return checks.reduce(
      (groups, check) => {
        const category = check.category;
        if (!groups[category]) {
          groups[category] = [];
        }
        groups[category].push(check);
        return groups;
      },
      {} as Record<string, GdprCheckResult[]>,
    );
  };

  const categoryGroups = groupChecksByCategory(scanResult.checks);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                GDPR Compliance Report
              </CardTitle>
              <CardDescription>
                {scanResult.targetUrl} • {new Date(scanResult.timestamp).toLocaleDateString()}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={getRiskLevelColor(scanResult.riskLevel)}>
                {getRiskLevelIcon(scanResult.riskLevel)}
                <span className="ml-1">{scanResult.riskLevel.toUpperCase()}</span>
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Overall Score */}
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">{scanResult.overallScore}%</div>
              <p className="text-sm text-muted-foreground">Overall Score</p>
              <Progress value={scanResult.overallScore} className="mt-2" />
            </div>

            {/* Passed Checks */}
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {scanResult.summary.passedChecks}
              </div>
              <p className="text-sm text-muted-foreground">Passed Checks</p>
              <p className="text-xs text-muted-foreground">
                of {scanResult.summary.totalChecks} total
              </p>
            </div>

            {/* Failed Checks */}
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">
                {scanResult.summary.failedChecks}
              </div>
              <p className="text-sm text-muted-foreground">Failed Checks</p>
              <p className="text-xs text-muted-foreground">
                {scanResult.summary.criticalFailures} critical
              </p>
            </div>

            {/* Manual Review */}
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600 mb-2">
                {scanResult.summary.manualReviewRequired}
              </div>
              <p className="text-sm text-muted-foreground">Manual Review</p>
              <p className="text-xs text-muted-foreground">Legal assessment needed</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Export Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Export Report</CardTitle>
          <CardDescription>Download the complete GDPR compliance report</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" onClick={() => handleExport('json')} disabled={isExporting}>
              <FileText className="h-4 w-4 mr-2" />
              JSON
            </Button>
            <Button variant="outline" onClick={() => handleExport('csv')} disabled={isExporting}>
              <Download className="h-4 w-4 mr-2" />
              CSV
            </Button>
            <Button variant="outline" onClick={() => handleExport('pdf')} disabled={isExporting}>
              <FileText className="h-4 w-4 mr-2" />
              PDF
            </Button>
          </div>

          {exportError && (
            <Alert variant="destructive" className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{exportError}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Detailed Results */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="checks">All Checks</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="manual-review">Manual Review</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Category Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Compliance by Category</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {scanResult.summary.categoryBreakdown.map((category) => (
                  <div key={category.category} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="capitalize">{category.category.replace('_', ' ')}</span>
                      <span>{category.score}%</span>
                    </div>
                    <Progress value={category.score} />
                    <p className="text-xs text-muted-foreground">
                      {category.passedInCategory}/{category.checksInCategory} checks passed
                    </p>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Scan Metadata */}
            <Card>
              <CardHeader>
                <CardTitle>Scan Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Scan Duration:</span>
                  <span>{Math.round(scanResult.scanDuration / 1000)}s</span>
                </div>
                <div className="flex justify-between">
                  <span>Analysis Levels:</span>
                  <span>{scanResult.metadata.analysisLevelsUsed.join(', ')}</span>
                </div>
                <div className="flex justify-between">
                  <span>Version:</span>
                  <span>{scanResult.metadata.version}</span>
                </div>
                <div className="flex justify-between">
                  <span>Checks Performed:</span>
                  <span>{scanResult.metadata.checksPerformed}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* All Checks Tab */}
        <TabsContent value="checks">
          <div className="space-y-6">
            {Object.entries(categoryGroups).map(([category, checks]) => (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="capitalize">
                    {category.replace('_', ' ')} ({checks.length} checks)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {checks.map((check) => (
                      <div
                        key={check.ruleId}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          {getCheckIcon(check)}
                          <div>
                            <p className="font-medium">{check.ruleName}</p>
                            <p className="text-sm text-muted-foreground">
                              {check.ruleId} • Score: {check.score}% • Weight: {check.weight}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={check.passed ? 'default' : 'destructive'}>
                            {check.passed ? 'Passed' : 'Failed'}
                          </Badge>
                          {check.manualReviewRequired && (
                            <Badge variant="outline">Manual Review</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Recommendations Tab */}
        <TabsContent value="recommendations">
          <Card>
            <CardHeader>
              <CardTitle>Improvement Recommendations</CardTitle>
              <CardDescription>Prioritized actions to improve GDPR compliance</CardDescription>
            </CardHeader>
            <CardContent>
              {scanResult.recommendations.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  No recommendations - excellent GDPR compliance!
                </p>
              ) : (
                <div className="space-y-4">
                  {scanResult.recommendations.map((rec) => (
                    <div key={rec.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium">{rec.title}</h4>
                        <div className="flex gap-2">
                          <Badge variant="outline">Priority {rec.priority}</Badge>
                          <Badge variant="secondary">{rec.effort}</Badge>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{rec.description}</p>
                      <p className="text-sm">
                        <strong>Implementation:</strong> {rec.implementation}
                      </p>
                      <p className="text-xs text-muted-foreground mt-2">
                        Timeline: {rec.timeline} • Impact: {rec.impact}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Manual Review Tab */}
        <TabsContent value="manual-review">
          <Card>
            <CardHeader>
              <CardTitle>Manual Review Required</CardTitle>
              <CardDescription>Items requiring human legal expertise assessment</CardDescription>
            </CardHeader>
            <CardContent>
              {scanResult.checks.filter((c) => c.manualReviewRequired).length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  No manual review items found
                </p>
              ) : (
                <div className="space-y-4">
                  {scanResult.checks
                    .filter((check) => check.manualReviewRequired)
                    .map((check) => (
                      <div key={check.ruleId} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium">{check.ruleName}</h4>
                          <Badge variant="outline">
                            <Clock className="h-3 w-3 mr-1" />
                            Manual Review
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {check.ruleId} • Category: {check.category}
                        </p>
                        <div className="space-y-2">
                          <h5 className="text-sm font-medium">Automated Findings:</h5>
                          {check.evidence.map((evidence, index) => (
                            <div key={index} className="text-sm bg-muted p-2 rounded">
                              <strong>{evidence.type}:</strong> {evidence.description}
                              {evidence.location && (
                                <div className="text-xs text-muted-foreground">
                                  Location: {evidence.location}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                          <p className="text-sm text-yellow-800">
                            <strong>Legal Review Required:</strong> This check requires human legal
                            expertise to determine compliance.
                          </p>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
