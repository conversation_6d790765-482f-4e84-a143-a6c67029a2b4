import { ApiResponse } from '@/types/gdpr';
import { GdprScanRequest, GdprScanResult, CookieAnalysisResult, ConsentAnalysisResult, TrackerAnalysisResult } from '@/types/gdpr';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export class GdprApiService {
  /**
   * Start GDPR compliance scan - REAL SCANNING ONLY
   */
  static async startScan(scanRequest: GdprScanRequest): Promise<GdprScanResult> {
    try {
      console.log('🔍 Starting GDPR scan:', scanRequest.targetUrl);

      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify(scanRequest),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<GdprScanResult> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Scan failed');
      }

      console.log('✅ GDPR scan completed:', apiResponse.data.scanId);
      return apiResponse.data;

    } catch (error) {
      console.error('❌ GDPR scan failed:', error);
      throw new Error(`GDPR scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get scan result by ID - REAL DATA ONLY
   */
  static async getScanResult(scanId: string): Promise<GdprScanResult> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<GdprScanResult> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Failed to retrieve scan result');
      }

      return apiResponse.data;

    } catch (error) {
      console.error('❌ Failed to retrieve GDPR scan result:', error);
      throw new Error(`Failed to retrieve scan result: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user's scan history - REAL DATA ONLY
   */
  static async getScanHistory(limit: number = 50, offset: number = 0): Promise<GdprScanResult[]> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/v1/compliance/gdpr/scans?limit=${limit}&offset=${offset}`,
        {
          headers: {
            'Authorization': `Bearer ${this.getAuthToken()}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<GdprScanResult[]> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Failed to retrieve scan history');
      }

      return apiResponse.data;

    } catch (error) {
      console.error('❌ Failed to retrieve GDPR scan history:', error);
      throw new Error(`Failed to retrieve scan history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get cookie analysis for scan - REAL DATA ONLY
   */
  static async getCookieAnalysis(scanId: string): Promise<CookieAnalysisResult[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}/cookies`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<CookieAnalysisResult[]> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Failed to retrieve cookie analysis');
      }

      return apiResponse.data;

    } catch (error) {
      console.error('❌ Failed to retrieve cookie analysis:', error);
      throw new Error(`Failed to retrieve cookie analysis: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export scan report - REAL DATA ONLY
   */
  static async exportScanReport(scanId: string, format: 'pdf' | 'json' | 'csv'): Promise<Blob> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify({ format }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.blob();

    } catch (error) {
      console.error('❌ Failed to export scan report:', error);
      throw new Error(`Failed to export scan report: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get authentication token
   */
  private static getAuthToken(): string {
    // TODO: Implement proper token retrieval from auth context
    return localStorage.getItem('authToken') || '';
  }
}
