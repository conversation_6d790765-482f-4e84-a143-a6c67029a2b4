/**
 * GDPR Compliance Orchestrator
 *
 * This class coordinates all GDPR compliance checks and manages the scanning process.
 * It follows the same pattern as the HIPAA orchestrator for consistency.
 */

import {
  GdprScanRequest,
  GdprScanResult,
  GdprCheckResult,
  GdprRecommendation,
  RiskLevel,
  ScanStatus,
  GdprCategory,
  CategoryBreakdown,
} from './types';
import { GDPR_SCORING_WEIGHTS } from './constants';
import { GdprDatabase } from './database/gdpr-database';
import { BrowserPoolManager } from './utils/browser-pool';
import { v4 as uuidv4 } from 'uuid';

export class GdprOrchestrator {
  private browserPool: BrowserPoolManager;

  constructor() {
    this.browserPool = BrowserPoolManager.getInstance();
  }

  /**
   * Perform comprehensive GDPR compliance scan
   * REAL WEBSITE SCANNING ONLY - NO MOCK DATA
   */
  async performComprehensiveScan(
    userId: string,
    scanRequest: GdprScanRequest,
  ): Promise<GdprScanResult> {
    const scanId = uuidv4();
    const startTime = Date.now();

    console.log(`🔍 Starting GDPR compliance scan for: ${scanRequest.targetUrl}`);
    console.log(`📋 Scan ID: ${scanId}`);

    try {
      // CRITICAL FIX: Create scan record FIRST before executing checks
      // This ensures foreign key constraints are satisfied when checks save data
      console.log(`📝 Creating scan record before executing checks...`);
      await GdprDatabase.createScan({
        userId,
        targetUrl: scanRequest.targetUrl,
        scanOptions: scanRequest.scanOptions || {},
        scanId // Use the pre-generated scanId
      });

      // Update status to running
      await GdprDatabase.updateScanStatus(scanId, 'running');

      // REAL SCANNING IMPLEMENTATION - Execute all 21 GDPR checks
      const checks = await this.executeAllGdprChecks(scanRequest, scanId);

      // Calculate overall score using risk-weighted algorithm
      const scoringResult = this.calculateGdprScore(checks);

      const scanDuration = Date.now() - startTime;

      // REAL SCAN RESULT - populated with actual check results
      const result: GdprScanResult = {
        scanId,
        targetUrl: scanRequest.targetUrl,
        timestamp: new Date().toISOString(),
        scanDuration,
        overallScore: scoringResult.overallScore,
        riskLevel: scoringResult.riskLevel,
        status: 'completed',
        summary: {
          totalChecks: checks.length,
          passedChecks: scoringResult.summary.passedChecks,
          failedChecks: scoringResult.summary.failedChecks,
          manualReviewRequired: scoringResult.summary.manualReviewRequired,
          criticalFailures: scoringResult.criticalFailures,
          categoryBreakdown: scoringResult.breakdown,
        },
        checks,
        recommendations: this.generateRecommendations(checks),
        metadata: {
          version: '1.0.0',
          processingTime: scanDuration,
          checksPerformed: checks.length,
          analysisLevelsUsed: ['pattern', 'behavioral', 'content', 'network'],
          errors: checks.filter((c) => !c.passed).map((c) => `${c.ruleName}: Failed`),
          warnings: checks
            .filter((c) => c.manualReviewRequired)
            .map((c) => `${c.ruleName}: Manual review required`),
          userAgent: scanRequest.scanOptions?.userAgent || 'GDPR-Compliance-Scanner/1.0',
          scanOptions: scanRequest.scanOptions,
        },
      };

      // Update existing scan record with final results
      await GdprDatabase.updateScanWithResults(scanId, result);
      await GdprDatabase.updateScanStatus(scanId, 'completed');

      console.log(`✅ GDPR scan completed successfully: ${scanId}`);
      return result;
    } catch (error) {
      console.error(`❌ GDPR scan failed: ${scanId}`, error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await GdprDatabase.updateScanStatus(scanId, 'failed', errorMessage);

      throw new Error(`GDPR scan failed: ${errorMessage}`);
    } finally {
      // Cleanup browser pool after scan
      await this.browserPool.cleanupIdleBrowsers();
    }
  }

  /**
   * Cleanup all browser resources
   * Call this when shutting down the application
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up GDPR orchestrator resources');
    await this.browserPool.cleanup();
  }

  /**
   * Execute all 21 GDPR compliance checks
   * REAL IMPLEMENTATION - calls actual check classes
   */
  private async executeAllGdprChecks(scanRequest: GdprScanRequest, scanId: string): Promise<GdprCheckResult[]> {
    const checks: GdprCheckResult[] = [];
    const config = {
      targetUrl: scanRequest.targetUrl,
      timeout: scanRequest.scanOptions?.timeout || 300000,
      scanId, // Pass the real scan ID to prevent UUID errors
    };

    try {
      // Import all check classes
      const {
        HttpsTlsCheck,
        PrivacyPolicyCheck,
        PrivacyContentCheck,
        CookieConsentCheck,
        CookieClassificationCheck,
        TrackerDetectionCheck,
        CookieAttributesCheck,
        GpcDntCheck,
        FormConsentCheck,
        SecurityHeadersCheck,
        IpAnonymizationCheck,
        DataRightsCheck,
        SpecialDataCheck,
        ChildrenConsentCheck,
        DpoContactCheck,
        DataTransfersCheck,
        BreachNotificationCheck,
        DpiaCheck,
        DataRetentionCheck,
        ProcessorAgreementsCheck,
        ImprintContactCheck,
      } = await import('./checks');

      // CRITICAL FIX: Execute checks SEQUENTIALLY to minimize resource load
      // This prevents resource exhaustion and provides better logging
      console.log(`🔍 Executing GDPR compliance checks for: ${config.targetUrl}`);
      console.log(`🛡️ Using SEQUENTIAL execution to minimize resource load`);

      const checkClasses = [
        { name: 'HTTPS/TLS Security', class: HttpsTlsCheck, requiresBrowser: false },
        { name: 'Security Headers', class: SecurityHeadersCheck, requiresBrowser: false },
        { name: 'IP Anonymization', class: IpAnonymizationCheck, requiresBrowser: false },
        { name: 'Privacy Policy Presence', class: PrivacyPolicyCheck, requiresBrowser: true },
        { name: 'Privacy Policy Content', class: PrivacyContentCheck, requiresBrowser: true },
        { name: 'Data Subject Rights', class: DataRightsCheck, requiresBrowser: true },
        { name: 'Cookie Consent Banner', class: CookieConsentCheck, requiresBrowser: true },
        { name: 'Cookie Classification', class: CookieClassificationCheck, requiresBrowser: true },
        { name: 'Cookie Attributes', class: CookieAttributesCheck, requiresBrowser: true },
        { name: 'GPC/DNT Support', class: GpcDntCheck, requiresBrowser: true },
        { name: 'Tracker Detection', class: TrackerDetectionCheck, requiresBrowser: true },
        { name: 'Form Consent Controls', class: FormConsentCheck, requiresBrowser: true },
        { name: 'Special Category Data', class: SpecialDataCheck, requiresBrowser: true },
        { name: 'Children Consent', class: ChildrenConsentCheck, requiresBrowser: true },
        { name: 'DPO Contact Information', class: DpoContactCheck, requiresBrowser: true },
        { name: 'International Data Transfers', class: DataTransfersCheck, requiresBrowser: true },
        { name: 'Breach Notification', class: BreachNotificationCheck, requiresBrowser: true },
        { name: 'Data Protection Impact Assessment', class: DpiaCheck, requiresBrowser: true },
        { name: 'Data Retention Policies', class: DataRetentionCheck, requiresBrowser: true },
        { name: 'Processor Agreements', class: ProcessorAgreementsCheck, requiresBrowser: true },
        { name: 'Imprint Contact Details', class: ImprintContactCheck, requiresBrowser: true },
      ];

      // Execute checks sequentially with detailed logging
      for (let i = 0; i < checkClasses.length; i++) {
        const checkInfo = checkClasses[i];
        const progress = `${i + 1}/${checkClasses.length}`;
        const browserIcon = checkInfo.requiresBrowser ? '🌐' : '🔧';

        console.log(`${browserIcon} [${progress}] Starting: ${checkInfo.name}`);

        try {
          const checkInstance = new checkInfo.class();
          const result = await checkInstance.performCheck(config);
          checks.push(result);

          const statusIcon = result.passed ? '✅' : '❌';
          const score = result.score ? ` (${result.score}%)` : '';
          console.log(`${statusIcon} [${progress}] Completed: ${checkInfo.name}${score}`);

        } catch (error) {
          console.error(`❌ [${progress}] Failed: ${checkInfo.name} - ${error instanceof Error ? error.message : 'Unknown error'}`);

          // Create a failed result for this check
          checks.push({
            ruleId: `GDPR-${String(i + 1).padStart(3, '0')}` as any,
            ruleName: checkInfo.name,
            category: 'technical' as any,
            passed: false,
            score: 0,
            weight: 1,
            severity: 'medium' as any,
            manualReviewRequired: false,
            evidence: [{
              type: 'text',
              description: 'Check execution failed',
              value: error instanceof Error ? error.message : 'Unknown error'
            }],
            recommendations: [{
              priority: 1,
              title: 'Check Execution Error',
              description: 'This check could not be completed due to a technical error.',
              implementation: 'Review the error logs and retry the scan.',
              effort: 'minimal',
              impact: 'medium'
            }]
          });
        }

        // No artificial delay - start next check immediately after previous completes
        // Browser pool will handle resource management automatically
      }



      console.log(`✅ Completed ${checks.length} GDPR checks`);
      return checks;
    } catch (error) {
      console.error('❌ Failed to execute GDPR checks:', error);
      throw new Error(
        `Check execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Calculate GDPR compliance score using risk-weighted algorithm
   */
  private calculateGdprScore(checks: GdprCheckResult[]): {
    overallScore: number;
    riskLevel: RiskLevel;
    criticalFailures: number;
    breakdown: CategoryBreakdown[];
    summary: {
      passedChecks: number;
      failedChecks: number;
      manualReviewRequired: number;
    };
  } {
    const weights = GDPR_SCORING_WEIGHTS;
    let totalWeightedScore = 0;
    let totalWeight = 0;
    let criticalFailures = 0;
    let passedChecks = 0;
    let manualReviewRequired = 0;

    for (const check of checks) {
      const weight = weights[check.ruleId] || 1;
      const score = check.passed ? 100 : 0;

      totalWeightedScore += score * weight;
      totalWeight += weight;

      if (check.passed) passedChecks++;
      if (check.manualReviewRequired) manualReviewRequired++;
      if (!check.passed && weight >= 7) criticalFailures++;
    }

    const baseScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
    const criticalPenalty = criticalFailures * 15;
    const finalScore = Math.max(0, baseScore - criticalPenalty);

    return {
      overallScore: Math.round(finalScore),
      riskLevel: this.determineRiskLevel(finalScore, criticalFailures),
      criticalFailures,
      breakdown: this.generateCategoryBreakdown(checks),
      summary: {
        passedChecks,
        failedChecks: checks.length - passedChecks,
        manualReviewRequired,
      },
    };
  }

  /**
   * Determine risk level based on score and critical failures
   */
  private determineRiskLevel(score: number, criticalFailures: number): RiskLevel {
    if (criticalFailures >= 3 || score < 40) return 'critical';
    if (criticalFailures >= 2 || score < 60) return 'high';
    if (criticalFailures >= 1 || score < 75) return 'medium';
    return 'low';
  }

  /**
   * Generate category breakdown for scoring
   */
  private generateCategoryBreakdown(checks: GdprCheckResult[]): CategoryBreakdown[] {
    const categoryMap = new Map<
      GdprCategory,
      {
        totalChecks: number;
        passedChecks: number;
      }
    >();

    for (const check of checks) {
      const existing = categoryMap.get(check.category) || {
        totalChecks: 0,
        passedChecks: 0,
      };

      existing.totalChecks++;
      if (check.passed) existing.passedChecks++;

      categoryMap.set(check.category, existing);
    }

    return Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      score: data.totalChecks > 0 ? Math.round((data.passedChecks / data.totalChecks) * 100) : 0,
      checksInCategory: data.totalChecks,
      passedInCategory: data.passedChecks,
    }));
  }

  /**
   * Generate recommendations based on check results
   */
  private generateRecommendations(checks: GdprCheckResult[]): GdprRecommendation[] {
    const recommendations: GdprRecommendation[] = [];
    let recommendationId = 1;

    for (const check of checks) {
      if (!check.passed && check.recommendations.length > 0) {
        for (const rec of check.recommendations) {
          recommendations.push({
            id: `REC-${recommendationId++}`,
            priority: rec.priority,
            title: rec.title,
            description: rec.description,
            category: check.category,
            effort: rec.effort,
            impact: rec.impact,
            timeline: this.getTimelineFromEffort(rec.effort),
            relatedRules: [check.ruleId],
          });
        }
      }
    }

    // Sort by priority
    return recommendations.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Get timeline estimate based on effort level
   */
  private getTimelineFromEffort(effort: string): string {
    switch (effort) {
      case 'minimal':
        return '1-2 days';
      case 'moderate':
        return '1-2 weeks';
      case 'significant':
        return '2-4 weeks';
      default:
        return '1-2 weeks';
    }
  }

  /**
   * Get scan status
   */
  async getScanStatus(scanId: string): Promise<ScanStatus | null> {
    try {
      const result = await GdprDatabase.getScanResult(scanId);
      return result?.status || null;
    } catch (error) {
      console.error('❌ Failed to get scan status:', error);
      return null;
    }
  }
}
